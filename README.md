# Bot de Monitoramento do Acunatix via Telegram

Um bot profissional para monitoramento em tempo real de scans do Acunatix via Telegram com interface avançada e IA integrada.

## Recursos Principais

🔍 **Scanner Automatizado**
- Monitorar progresso dos scans e receber notificações via Telegram
- Iniciar novos scans de SQL Injection, XSS, OWASP Top 10 diretamente pelo Telegram
- Suporte para login automatizado com geração de arquivo LSR usando IA
- Integração com OpenAI Vision API para análise inteligente de formulários

🤖 **Interface Profissional**
- Interface completa com menus interativos e navegação por botões
- Sistema de templates personalizáveis para diferentes usuários
- Dashboard em tempo real com estatísticas detalhadas
- Persistência de sessões - não precisa fazer login toda vez

⚡ **Sistema Avançado**
- Sistema multi-usuário com filas independentes
- Rate limiting e controle de concorrência
- Gerenciamento inteligente de filas de scan
- Autenticação segura para usuários e administradores

## Arquitetura do Sistema

### Componentes Principais

- **`monitor-bot-professional.js`** - Bot principal com interface avançada
- **`scan-queue-manager.js`** - Gerenciamento inteligente de filas de scan
- **`user-session-manager.js`** - Controle de sessões e autenticação
- **`bot-interface.js`** - Interface de usuário e menus interativos
- **`template-manager.js`** - Sistema de templates personalizáveis
- **`add-target.js`** - Integração principal com API do Acunatix
- **`login-sequence-generator-mcp.js`** - Gerador de login com IA
- **`mcp-browser-server.js`** - Servidor MCP para automação web
- **`acunatix-controller.js`** - Controlador do Acunatix
- **`config.js`** - Configurações centralizadas

### Diretórios

- **`data/`** - Armazenamento de sessões e dados persistentes
- **`lsr_files/`** - Arquivos LSR gerados automaticamente

## Funcionalidade de Login Automatizado

### Como usar o scan com login

O bot agora suporta scans em sites que requerem autenticação:

```
/scan https://site.com -l usuario:senha
```

O bot irá:
1. Analisar a página de login usando IA (OpenAI Vision API)
2. Identificar os campos de login automaticamente
3. Gerar um arquivo LSR (Login Sequence Recorder)
4. Configurar o scan no Acunetix com autenticação

### Requisitos para Login Automatizado

- Chave de API da OpenAI configurada em `config.js`
- Site com formulário de login tradicional (username/password)
- Credenciais válidas para o site

## Instalação

1. Certifique-se de que você tem o Node.js instalado no seu sistema.

2. Execute o script de instalação:
```
instalar.bat
```

## Configuração

Todas as configurações estão armazenadas no arquivo `config.js`. Você pode modificar as seguintes configurações:

- `TELEGRAM_BOT_TOKEN`: Token do seu bot do Telegram
- `BOT_PASSWORD`: Senha para autenticação de usuários no Telegram
- `ADMIN_PASSWORD`: Senha para acesso administrativo
- `ACUNATIX_URL`: URL da sua instância do Acunatix
- `ACUNATIX_USERNAME`: Nome de usuário de login do Acunatix
- `ACUNATIX_PASSWORD`: Senha de login do Acunatix
- `SCAN_CHECK_INTERVAL`: Intervalo para verificação do status dos scans (em milissegundos)
- `OPENAI_API_KEY`: Chave de API da OpenAI (necessária para login automatizado)

## Uso

### Iniciando o Bot

Execute o arquivo:
```
monitor.bat
```

### Comandos do Bot do Telegram

### **Comandos via Texto:**
- `/start` - Iniciar o bot e autenticar (você precisará informar a senha)
- `/scan [url]` - Iniciar um scan normal para a URL especificada
- `/scan [url] -l user:pass` - **NOVO!** Iniciar scan com login automatizado usando IA

### **Comandos via Menu Interativo:**
- **🔍 Novo Scan** - Interface completa para configurar scans
- **📊 Status** - Dashboard em tempo real
- **📋 Fila** - Gerenciar fila de scans
- **📚 Histórico** - Ver scans anteriores
- **👤 Conta** - Configurações pessoais
- **🔧 Admin** - Painel administrativo (apenas admins)

## Solução de Problemas

- Se o bot não conseguir se conectar ao Acunatix, verifique a URL, nome de usuário e senha no arquivo de configuração.
- Para problemas de certificados SSL autoassinados, o bot está configurado para ignorá-los.
- Se o bot do Telegram não estiver respondendo, verifique seu token de bot.
- Para problemas relacionados ao puppeteer, verifique os logs do console para mensagens de erro detalhadas.
- Se o login automatizado falhar, verifique:
  - Se a chave da API OpenAI está configurada corretamente
  - Se o site tem um formulário de login tradicional
  - Se as credenciais estão corretas
  - Os logs mostrarão os seletores encontrados pela IA

## Notas de Segurança

- O bot requer uma senha para autenticar usuários
- Apenas usuários autenticados receberão notificações de scan
- Todas as credenciais são armazenadas no arquivo config.js, mantenha-o seguro
- As sessões são salvas localmente em `/data/sessions.json`
- Sessões expiram após 30 dias de inatividade