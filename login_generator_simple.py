#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerador de Login Simplificado
Gera arquivo LSR simulado para testes
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# Configurar encoding para Windows
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

class LoginGeneratorSimple:
    def __init__(self):
        print("LoginGeneratorSimple inicializado")

    async def generate_login_sequence(self, url, username, password):
        """
        Gera sequência de login simulada para testes
        """
        print(f"Gerando login para: {url}")
        print(f"Usuario: {username}")
        print(f"Senha: {'*' * len(password)}")
        
        # Simular ações de login
        actions = [
            {
                "type": "navigate",
                "parameters": {"url": url},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "takeScreenshot",
                "parameters": {},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "findElements",
                "parameters": {"description": "username field"},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "typeText",
                "parameters": {"selector": "input[type='email']", "text": username},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "findElements",
                "parameters": {"description": "password field"},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "typeText",
                "parameters": {"selector": "input[type='password']", "text": "[PASSWORD]"},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "findElements",
                "parameters": {"description": "submit button"},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "clickElement",
                "parameters": {"selector": "button[type='submit']"},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "waitForNavigation",
                "parameters": {"timeout": 5000},
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "checkLoginSuccess",
                "parameters": {},
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        # Gerar arquivo LSR
        lsr_data = self._generate_lsr(actions, url, username)
        
        # Salvar arquivo LSR
        filename = f"login_{int(datetime.now().timestamp())}.lsr"
        filepath = Path("./lsr_files") / filename
        
        # Criar diretório se não existir
        filepath.parent.mkdir(exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(lsr_data, f, indent=2, ensure_ascii=False)
        
        print(f"Arquivo LSR salvo: {filename}")
        
        return {
            "success": True,
            "actions": actions,
            "lsr": lsr_data,
            "filename": filename,
            "path": str(filepath),
            "message": f"Login sequence gerada com {len(actions)} acoes"
        }

    def _generate_lsr(self, actions, url, username):
        """
        Gera estrutura LSR das ações executadas
        """
        lsr_actions = []
        
        for action in actions:
            lsr_action = {
                "type": action["type"],
                "target": action["parameters"].get("selector") or action["parameters"].get("url") or "",
                "parameters": {
                    "friendly": f"{action['type']} - {action['parameters'].get('selector', action['parameters'].get('url', ''))}",
                    **action["parameters"]
                }
            }
            lsr_actions.append(lsr_action)
        
        return {
            "version": "1.0",
            "url": url,
            "username": username,
            "actions": lsr_actions,
            "metadata": {
                "generated_by": "LoginGeneratorSimple",
                "timestamp": datetime.now().isoformat(),
                "total_actions": len(lsr_actions)
            }
        }

async def main():
    """
    Função principal para teste
    """
    if len(sys.argv) != 4:
        print("Uso: python login_generator_simple.py <url> <username> <password>")
        sys.exit(1)
    
    url = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    
    try:
        generator = LoginGeneratorSimple()
        result = await generator.generate_login_sequence(url, username, password)
        
        print("\nResultado:")
        print(f"Sucesso: {result['success']}")
        print(f"Acoes: {len(result['actions'])}")
        print(f"Arquivo: {result['filename']}")
        print(f"Mensagem: {result['message']}")
        
        # Retornar resultado como JSON para integração com Node.js
        print("\nJSON Result:")
        print(json.dumps(result, indent=2, default=str))
        
    except Exception as e:
        print(f"Erro: {e}")
        sys.exit(1)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
