/**
 * Cliente MCP para Node.js
 * Baseado no padrão lastmile-ai/openai-agents-mcp
 * Integra OpenAI com servidores MCP
 */

const { spawn } = require('child_process');
const { EventEmitter } = require('events');
const OpenAI = require('openai');
const config = require('./config');

class MCPClient extends EventEmitter {
  constructor() {
    super();
    this.servers = new Map();
    this.openai = new OpenAI({
      apiKey: config.OPENAI_API_KEY
    });
  }

  /**
   * Conecta a um servidor MCP
   */
  async connectServer(serverName, serverConfig) {
    console.log(`🔌 Conectando ao servidor MCP: ${serverName}`);
    
    try {
      const process = spawn(serverConfig.command, serverConfig.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      const server = {
        name: serverName,
        process: process,
        config: serverConfig,
        tools: [],
        connected: false
      };

      // Aguardar inicialização
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Timeout ao conectar servidor ${serverName}`));
        }, 10000);

        process.stdout.on('data', (data) => {
          const output = data.toString();
          if (output.includes('Server started') || output.includes('ready')) {
            clearTimeout(timeout);
            server.connected = true;
            resolve();
          }
        });

        process.stderr.on('data', (data) => {
          console.error(`Erro no servidor ${serverName}:`, data.toString());
        });

        process.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      this.servers.set(serverName, server);
      console.log(`✅ Servidor ${serverName} conectado com sucesso`);
      
      return server;
    } catch (error) {
      console.error(`❌ Erro ao conectar servidor ${serverName}:`, error);
      throw error;
    }
  }

  /**
   * Desconecta um servidor MCP
   */
  async disconnectServer(serverName) {
    const server = this.servers.get(serverName);
    if (server && server.process) {
      server.process.kill();
      this.servers.delete(serverName);
      console.log(`🔌 Servidor ${serverName} desconectado`);
    }
  }

  /**
   * Desconecta todos os servidores
   */
  async disconnectAll() {
    for (const [serverName] of this.servers) {
      await this.disconnectServer(serverName);
    }
  }

  /**
   * Gera sequência de login usando OpenAI + MCP
   */
  async generateLoginSequence(url, username, password) {
    console.log('🤖 Iniciando geração de sequência de login com OpenAI + MCP...');

    try {
      // Definir ferramentas MCP disponíveis
      const tools = [
        {
          type: "function",
          function: {
            name: "navigate",
            description: "Navigate to a URL",
            parameters: {
              type: "object",
              properties: {
                url: { type: "string", description: "URL to navigate to" }
              },
              required: ["url"]
            }
          }
        },
        {
          type: "function",
          function: {
            name: "takeScreenshot",
            description: "Take a screenshot of the current page",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        },
        {
          type: "function",
          function: {
            name: "findElements",
            description: "Find elements on the page by description",
            parameters: {
              type: "object",
              properties: {
                description: { type: "string", description: "Description of elements to find" }
              },
              required: ["description"]
            }
          }
        },
        {
          type: "function",
          function: {
            name: "typeText",
            description: "Type text into an element",
            parameters: {
              type: "object",
              properties: {
                selector: { type: "string", description: "CSS selector of element" },
                text: { type: "string", description: "Text to type" }
              },
              required: ["selector", "text"]
            }
          }
        },
        {
          type: "function",
          function: {
            name: "clickElement",
            description: "Click an element",
            parameters: {
              type: "object",
              properties: {
                selector: { type: "string", description: "CSS selector of element" }
              },
              required: ["selector"]
            }
          }
        },
        {
          type: "function",
          function: {
            name: "checkLoginSuccess",
            description: "Check if login was successful",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        }
      ];

      // Prompt do sistema
      const systemPrompt = `You are an expert browser automation assistant. You will help login to websites by controlling a browser through MCP tools.

Your task is to complete the ENTIRE login process step by step:

1. Navigate to the login page
2. Take a screenshot to analyze the page
3. Find login form elements (username/email field, password field, submit button)
4. Type the username in the appropriate field
5. Type the password in the appropriate field
6. Click the submit/login button
7. Take another screenshot to verify login
8. Check if login was successful

IMPORTANT RULES:
- You MUST complete ALL steps, don't stop after just navigating
- Always take screenshots to analyze what you see
- Use findElements to locate form fields before typing
- Verify each action succeeded before proceeding
- Continue until login is complete or you determine it failed

The user is counting on you to complete the full login sequence!`;

      // Mensagens da conversa
      const messages = [
        { role: "system", content: systemPrompt },
        { 
          role: "user", 
          content: `Please login to ${url} with these credentials:\nUsername: ${username}\nPassword: [will be provided when typing]\n\nStart by navigating to the URL and analyzing the page.`
        }
      ];

      // Processar conversa com múltiplas iterações
      const result = await this.processConversation(messages, tools, { url, username, password });

      return result;

    } catch (error) {
      console.error('❌ Erro ao gerar sequência de login:', error);
      throw error;
    }
  }

  /**
   * Processa conversa com múltiplas iterações até completar o login
   */
  async processConversation(messages, tools, credentials) {
    const actions = [];
    const maxIterations = 10;
    let iteration = 0;

    while (iteration < maxIterations) {
      iteration++;
      console.log(`🔄 Iteração ${iteration}/${maxIterations}`);

      // Chamar OpenAI
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: messages,
        tools: tools,
        tool_choice: "auto",
        max_tokens: 2000
      });

      const assistantMessage = response.choices[0].message;

      // Adicionar resposta da IA às mensagens
      messages.push(assistantMessage);

      // Se não há tool calls, a IA terminou
      if (!assistantMessage.tool_calls || assistantMessage.tool_calls.length === 0) {
        console.log('✅ IA concluiu o processo de login');
        break;
      }

      // Processar tool calls
      for (const toolCall of assistantMessage.tool_calls) {
        const { name, arguments: args } = toolCall.function;
        const parsedArgs = JSON.parse(args);

        console.log(`🔧 Executando ferramenta: ${name}`, parsedArgs);

        // Executar ferramenta MCP
        const result = await this.executeMCPTool(name, parsedArgs, credentials);

        actions.push({
          type: name,
          parameters: parsedArgs,
          result: result
        });

        // Adicionar resultado às mensagens
        messages.push({
          role: "tool",
          tool_call_id: toolCall.id,
          content: JSON.stringify(result)
        });
      }

      // Verificar se login foi bem-sucedido
      if (actions.some(action => action.type === 'checkLoginSuccess' && action.result.loggedIn)) {
        console.log('✅ Login verificado com sucesso!');
        break;
      }
    }

    // Gerar arquivo LSR das ações
    const lsrData = this.generateLSRFromActions(actions);

    return {
      success: actions.length > 1, // Sucesso se fez mais que apenas navegar
      actions: actions,
      lsr: lsrData,
      filename: `login_${Date.now()}.lsr`,
      path: `./lsr_files/login_${Date.now()}.lsr`
    };
  }

  /**
   * Executa ferramenta MCP (simulado por enquanto)
   */
  async executeMCPTool(toolName, parameters, credentials) {
    // Por enquanto, simular execução
    // Na implementação real, isso chamaria o servidor MCP
    
    switch (toolName) {
      case 'navigate':
        return { success: true, url: parameters.url };
      
      case 'takeScreenshot':
        return { success: true, screenshot: 'base64_screenshot_data' };
      
      case 'findElements':
        // Simular encontrar elementos de login
        return {
          success: true,
          elements: [
            { selector: 'input[type="email"]', description: 'Email field' },
            { selector: 'input[type="password"]', description: 'Password field' },
            { selector: 'button[type="submit"]', description: 'Submit button' }
          ]
        };
      
      case 'typeText':
        // Substituir senha real por placeholder
        const text = parameters.text === credentials.password ? '[PASSWORD]' : parameters.text;
        return { success: true, selector: parameters.selector, text: text };
      
      case 'clickElement':
        return { success: true, selector: parameters.selector };
      
      case 'checkLoginSuccess':
        return { success: true, loggedIn: true };
      
      default:
        return { success: false, error: `Ferramenta ${toolName} não implementada` };
    }
  }

  /**
   * Gera arquivo LSR das ações executadas
   */
  generateLSRFromActions(actions) {
    const lsrActions = actions.map(action => ({
      type: action.type,
      target: action.parameters.selector || action.parameters.url || '',
      parameters: {
        friendly: `${action.type} - ${action.parameters.selector || action.parameters.url || ''}`,
        ...action.parameters
      }
    }));

    return {
      version: "1.0",
      actions: lsrActions,
      metadata: {
        generated_by: "MCP Login Generator",
        timestamp: new Date().toISOString()
      }
    };
  }
}

module.exports = MCPClient;
