# Configuração MCP Agent para Bot Acunatix
# Baseado no padrão lastmile-ai/openai-agents-mcp

# Configurações de execução
execution_engine: asyncio

# Configurações de logging
logger:
  transports: [console, file]
  level: info
  path: "logs/mcp-agent.jsonl"

# Configurações OpenAI
openai:
  default_model: gpt-4o
  # API key será definida em variável de ambiente OPENAI_API_KEY

# Configurações dos servidores MCP
mcp:
  servers:
    # Servidor de browser automation usando Puppeteer
    browser:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-puppeteer"]
      description: "Browser automation server for login sequences"
      
    # Servidor de filesystem para LSR files
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem", "./lsr_files"]
      description: "Filesystem access for LSR file management"
