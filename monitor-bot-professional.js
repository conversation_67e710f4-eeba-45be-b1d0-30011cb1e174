const puppeteer = require('puppeteer');
const TelegramBot = require('node-telegram-bot-api');
const config = require('./config');
const { addTargetAndStartSQLIScan } = require('./add-target');
const ScanQueueManager = require('./scan-queue-manager');
const UserSessionManager = require('./user-session-manager');
const BotInterface = require('./bot-interface');
const TemplateManager = require('./template-manager');
const { spawn } = require('child_process');

// Inicializar componentes
const scanQueue = new ScanQueueManager();
const sessionManager = new UserSessionManager();
const bot = new TelegramBot(config.TELEGRAM_BOT_TOKEN, { polling: true });
const interface = new BotInterface(bot);
const templateManager = new TemplateManager();

// Estado do usuário para menus
const userState = new Map();

// Função para escapar caracteres especiais do Markdown
function escapeMarkdown(text) {
  if (!text) return '';
  return text.toString()
    .replace(/[_*[\]()~`>#+=|{}.!-]/g, '\\$&');
}

// Configurar listeners da fila
scanQueue.on('scan:started', (scan) => {
  const message = templateManager.applyTemplate(scan.userId, 'notification', {
    title: 'Scan Iniciado',
    content: interface.formatNotification('scan_started', scan)
  });
  
  bot.sendMessage(scan.userId, message, { 
    parse_mode: 'Markdown',
    reply_markup: interface.getMainMenu(sessionManager.getSessionInfo(scan.userId).isAdmin)
  });
});

scanQueue.on('scan:completed', (scan) => {
  const vulnSummary = scan.result?.vulnerabilities 
    ? `🔴 ${scan.result.vulnerabilities.critical || 0} | 🟠 ${scan.result.vulnerabilities.high || 0} | 🟡 ${scan.result.vulnerabilities.medium || 0}`
    : 'Nenhuma vulnerabilidade encontrada';
    
  const message = interface.formatNotification('scan_completed', {
    target: scan.target,
    duration: `${Math.round((scan.endTime - scan.startTime) / 1000)}s`,
    vulnerabilities: vulnSummary
  });
  
  bot.sendMessage(scan.userId, message, { parse_mode: 'Markdown' });
});

// Comando /start com menu
bot.onText(/\/start/, async (msg) => {
  const chatId = msg.chat.id;
  const username = msg.from.username || msg.from.first_name || 'User';

  const session = sessionManager.getOrCreateSession(chatId, username);

  if (sessionManager.isAuthenticated(chatId)) {
    const welcomeMessage = templateManager.applyTemplate(chatId, 'welcome', {
      content: `Olá ${username}! O que deseja fazer hoje?`
    });

    await bot.sendMessage(chatId, welcomeMessage, {
      parse_mode: 'Markdown',
      reply_markup: interface.getMainMenu(session.isAdmin)
    });
  } else {
    await bot.sendMessage(chatId,
      `🔐 **Autenticação Necessária**\n\n` +
      `Por favor, digite a senha para continuar:`,
      { parse_mode: 'Markdown' }
    );
  }
});

// Comando /scan com suporte a login
bot.onText(/\/scan (.+)/, async (msg, match) => {
  const chatId = msg.chat.id;

  if (!sessionManager.isAuthenticated(chatId)) {
    await bot.sendMessage(chatId, '⚠️ Você precisa se autenticar primeiro. Use /start');
    return;
  }

  try {
    const input = match[1].trim();
    const parsedCommand = parseCommand(input);

    if (!parsedCommand.url) {
      await bot.sendMessage(chatId, '❌ URL inválida. Use: /scan https://site.com ou /scan https://site.com -l usuario:senha');
      return;
    }

    // Mostrar status inicial
    const statusMessage = await bot.sendMessage(chatId,
      `🔍 **Iniciando Scan**\n\n` +
      `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
      `🔐 Login: ${parsedCommand.credentials ? '✅ Sim' : '❌ Não'}\n` +
      `⏳ Status: Preparando...`,
      { parse_mode: 'Markdown' }
    );

    if (parsedCommand.credentials) {
      // Scan com login usando MCP
      await handleScanWithLogin(chatId, statusMessage.message_id, parsedCommand);
    } else {
      // Scan normal
      await handleNormalScan(chatId, statusMessage.message_id, parsedCommand);
    }

  } catch (error) {
    console.error('Erro no comando /scan:', error);
    await bot.sendMessage(chatId, `❌ Erro ao processar comando: ${error.message}`);
  }
});

// Processar callbacks dos botões
bot.on('callback_query', async (callbackQuery) => {
  const chatId = callbackQuery.message.chat.id;
  const data = callbackQuery.data;
  const messageId = callbackQuery.message.message_id;
  
  // Verificar autenticação
  if (!sessionManager.isAuthenticated(chatId) && !data.startsWith('auth_')) {
    await bot.answerCallbackQuery(callbackQuery.id, {
      text: 'Sessão expirada. Use /start',
      show_alert: true
    });
    return;
  }
  
  // Processar ações do menu
  switch (data) {
    case 'menu_main':
      await handleMainMenu(chatId, messageId);
      break;
      
    case 'menu_scan':
      await handleScanMenu(chatId, messageId);
      break;
      
    case 'menu_status':
      await handleStatusMenu(chatId, messageId);
      break;
      
    case 'menu_queue':
      await handleQueueMenu(chatId, messageId);
      break;
      
    case 'menu_history':
      await handleHistoryMenu(chatId, messageId, 1);
      break;
      
    case 'menu_account':
      await handleAccountMenu(chatId, messageId);
      break;
      
    case 'menu_admin':
      await handleAdminMenu(chatId, messageId);
      break;
      
    case 'menu_dashboard':
      await handleDashboard(chatId, messageId);
      break;
      
    case 'menu_refresh':
      await handleRefresh(chatId, messageId);
      break;
      
    case 'menu_logout':
      sessionManager.logout(chatId);
      await bot.editMessageText('👋 Sessão encerrada com sucesso!', {
        chat_id: chatId,
        message_id: messageId
      });
      break;
  }
  
  // Processar perfis de scan
  if (data.startsWith('scan_')) {
    await handleScanProfile(chatId, messageId, data);
  }
  
  // Processar paginação
  if (data.includes('_page_')) {
    await handlePagination(chatId, messageId, data);
  }
  
  // Processar templates
  if (data.startsWith('template_')) {
    await handleTemplate(chatId, messageId, data);
  }
  
  // Responder callback
  await bot.answerCallbackQuery(callbackQuery.id);
});

// Handler do menu principal
async function handleMainMenu(chatId, messageId) {
  const session = sessionManager.getSessionInfo(chatId);
  const welcomeMessage = templateManager.applyTemplate(chatId, 'welcome', {
    content: `Olá ${session.username}! O que deseja fazer?`
  });
  
  await bot.editMessageText(welcomeMessage, {
    chat_id: chatId,
    message_id: messageId,
    parse_mode: 'Markdown',
    reply_markup: interface.getMainMenu(session.isAdmin)
  });
}

// Handler do menu de scan
async function handleScanMenu(chatId, messageId) {
  const state = userState.get(chatId) || {};
  
  if (!state.scanTarget) {
    // Pedir URL
    userState.set(chatId, { ...state, awaitingTarget: true });
    
    await bot.editMessageText(
      '🔍 **Novo Scan**\n\n' +
      'Por favor, envie a URL que deseja escanear:',
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [[
            { text: '↩️ Cancelar', callback_data: 'menu_main' }
          ]]
        }
      }
    );
  } else {
    // Mostrar perfis
    await bot.editMessageText(
      `🔍 **Novo Scan**\n\n` +
      `🎯 Target: \`${state.scanTarget}\`\n\n` +
      `Selecione o perfil de scan:`,
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown',
        reply_markup: interface.getScanProfileMenu()
      }
    );
  }
}

// Handler do status
async function handleStatusMenu(chatId, messageId) {
  // Coletar estatísticas
  const stats = {
    activeScans: scanQueue.getGlobalStats().totalActive,
    queuedScans: scanQueue.getGlobalStats().totalQueued,
    completedToday: 15, // TODO: Implementar contador real
    vulnerabilities: {
      critical: 2,
      high: 5,
      medium: 12,
      low: 23,
      info: 45
    }
  };
  
  const statusMessage = interface.formatStatusMessage(stats);
  
  await bot.editMessageText(statusMessage, {
    chat_id: chatId,
    message_id: messageId,
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔄 Atualizar', callback_data: 'menu_status' },
          { text: '📊 Detalhes', callback_data: 'status_details' }
        ],
        [
          { text: '↩️ Menu Principal', callback_data: 'menu_main' }
        ]
      ]
    }
  });
}

// Handler da fila
async function handleQueueMenu(chatId, messageId) {
  const queueStatus = scanQueue.getUserQueueStatus(chatId);
  const template = templateManager.getUserTemplate(chatId);
  
  let message = '📋 **Sua Fila de Scans**\n\n';
  
  if (queueStatus.active > 0) {
    message += '🔄 **Em Execução:**\n';
    queueStatus.activeScans.forEach((scan, i) => {
      const elapsed = Math.round((Date.now() - scan.startTime) / 1000);
      if (template.features.showProgressBars) {
        const progress = interface.createProgressBar(elapsed, 300); // estimativa 5min
        message += `${i + 1}. ${scan.target}\n   ${progress}\n`;
      } else {
        message += `${i + 1}. ${scan.target} (${elapsed}s)\n`;
      }
    });
    message += '\n';
  }
  
  if (queueStatus.queued > 0) {
    message += '⏳ **Aguardando:**\n';
    queueStatus.queue.forEach((scan, i) => {
      message += `${scan.position}. ${scan.target}\n`;
    });
  }
  
  if (queueStatus.active === 0 && queueStatus.queued === 0) {
    message += '✅ Nenhum scan em andamento ou na fila';
  }
  
  await bot.editMessageText(message, {
    chat_id: chatId,
    message_id: messageId,
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔄 Atualizar', callback_data: 'menu_queue' },
          { text: '➕ Novo Scan', callback_data: 'menu_scan' }
        ],
        [
          { text: '↩️ Menu Principal', callback_data: 'menu_main' }
        ]
      ]
    }
  });
}

// Handler do histórico com paginação
async function handleHistoryMenu(chatId, messageId, page = 1) {
  const itemsPerPage = 5;
  const history = scanQueue.getUserHistory(chatId, 100);
  const totalPages = Math.ceil(history.length / itemsPerPage);
  
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageItems = history.slice(startIndex, endIndex);
  
  let message = '📚 **Histórico de Scans**\n\n';
  
  if (pageItems.length === 0) {
    message += 'Nenhum scan encontrado';
  } else {
    pageItems.forEach((scan, i) => {
      message += interface.formatHistoryItem(scan, startIndex + i + 1);
    });
  }
  
  // Salvar dados de paginação
  interface.paginationData.set(`${chatId}_history`, { 
    data: history, 
    itemsPerPage 
  });
  
  await bot.editMessageText(message, {
    chat_id: chatId,
    message_id: messageId,
    parse_mode: 'Markdown',
    reply_markup: interface.getPaginationMenu(page, totalPages, 'history')
  });
}

// Handler da conta
async function handleAccountMenu(chatId, messageId) {
  const info = sessionManager.getSessionInfo(chatId);
  const currentTemplate = templateManager.userTemplates.get(chatId) || 'default';
  
  const message = `
👤 **Minha Conta**
━━━━━━━━━━━━━━━━━

📊 **Informações**
• Usuário: ${info.username}
• Tipo: ${info.isAdmin ? '🔧 Administrador' : '👤 Usuário'}
• Sessão: ${Math.round(info.sessionAge / 60000)} minutos

📈 **Limites de Uso**
• Scans/hora: ${info.scanCount.hour}/${info.scanCount.hourlyLimit}
• Scans/dia: ${info.scanCount.day}/${info.scanCount.dailyLimit}

🎨 **Template**: ${templateManager.getUserTemplate(chatId).name}
`;

  await bot.editMessageText(message, {
    chat_id: chatId,
    message_id: messageId,
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🎨 Alterar Template', callback_data: 'account_template' },
          { text: '🔔 Notificações', callback_data: 'account_notifications' }
        ],
        [
          { text: '📊 Estatísticas', callback_data: 'account_stats' },
          { text: '🔐 Segurança', callback_data: 'account_security' }
        ],
        [
          { text: '↩️ Menu Principal', callback_data: 'menu_main' }
        ]
      ]
    }
  });
}

// Processar mensagens de texto (para entrada de URL)
bot.on('message', async (msg) => {
  const chatId = msg.chat.id;
  
  if (msg.text && msg.text.startsWith('/')) return;
  
  const state = userState.get(chatId) || {};
  
  // Se aguardando URL para scan
  if (state.awaitingTarget && sessionManager.isAuthenticated(chatId)) {
    const target = msg.text.trim();
    
    if (!target.match(/^https?:\/\/.+/)) {
      await bot.sendMessage(chatId, '⚠️ URL inválida. Use http:// ou https://');
      return;
    }
    
    userState.set(chatId, { ...state, scanTarget: target, awaitingTarget: false });
    
    // Mostrar menu de perfis
    await bot.sendMessage(chatId,
      `🔍 **Novo Scan**\n\n` +
      `🎯 Target: \`${target}\`\n\n` +
      `Selecione o perfil de scan:`,
      {
        parse_mode: 'Markdown',
        reply_markup: interface.getScanProfileMenu()
      }
    );
    
    return;
  }
  
  // Autenticação
  if (!sessionManager.isAuthenticated(chatId)) {
    const authenticated = sessionManager.authenticateUser(
      chatId, 
      msg.text, 
      config.ADMIN_PASSWORD
    );
    
    if (authenticated) {
      const session = sessionManager.getSessionInfo(chatId);
      await bot.sendMessage(chatId,
        `✅ **Autenticação bem-sucedida!**\n\n` +
        `Bem-vindo, ${session.username}!`,
        {
          parse_mode: 'Markdown',
          reply_markup: interface.getMainMenu(session.isAdmin)
        }
      );
    } else {
      await bot.sendMessage(chatId, '❌ Senha incorreta. Tente novamente.');
    }
  }
});

// Processar seleção de perfil de scan
async function handleScanProfile(chatId, messageId, profile) {
  const state = userState.get(chatId) || {};
  
  if (!state.scanTarget) {
    await bot.editMessageText('❌ Erro: Nenhum target definido', {
      chat_id: chatId,
      message_id: messageId
    });
    return;
  }
  
  const profileMap = {
    'scan_sqli': 'SQL Injection',
    'scan_xss': 'Cross-site Scripting',
    'scan_full': 'Full Scan',
    'scan_quick': 'Quick Scan',
    'scan_owasp': 'OWASP Top 10',
    'scan_passwords': 'Weak Passwords'
  };
  
  const selectedProfile = profileMap[profile] || 'SQL Injection';
  
  // Adicionar à fila
  const result = await scanQueue.addScan(
    chatId,
    state.scanTarget,
    `${selectedProfile} - ${new Date().toISOString()}`
  );
  
  if (result.success) {
    await bot.editMessageText(
      interface.formatNotification('queue_update', {
        message: `✅ Scan adicionado à fila com sucesso!`,
        position: result.position,
        estimatedTime: `${result.position * 5} minutos`
      }),
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '📋 Ver Fila', callback_data: 'menu_queue' },
              { text: '➕ Novo Scan', callback_data: 'menu_scan' }
            ],
            [
              { text: '↩️ Menu Principal', callback_data: 'menu_main' }
            ]
          ]
        }
      }
    );
  } else {
    await bot.editMessageText(`⚠️ ${result.message}`, {
      chat_id: chatId,
      message_id: messageId,
      reply_markup: {
        inline_keyboard: [[
          { text: '↩️ Menu Principal', callback_data: 'menu_main' }
        ]]
      }
    });
  }
  
  // Limpar estado
  userState.delete(chatId);
}

// Handler do dashboard admin
async function handleDashboard(chatId, messageId) {
  if (!sessionManager.hasPermission(chatId, 'admin')) {
    await bot.answerCallbackQuery(callbackQuery.id, {
      text: 'Acesso negado',
      show_alert: true
    });
    return;
  }
  
  const data = {
    users: {
      online: sessionManager.getActiveSessions().length,
      todayActive: 12,
      monthActive: 45,
      topToday: [
        { name: 'user1', scans: 15, status: 'ativo' },
        { name: 'user2', scans: 8, status: 'ativo' },
        { name: 'admin', scans: 5, status: 'idle' }
      ]
    },
    scans: {
      hourlyActivity: [2, 3, 1, 0, 0, 1, 4, 8, 12, 15, 14, 16, 18, 15, 12, 10, 8, 6, 5, 4, 3, 2, 1, 1]
    },
    performance: {
      cpu: '23',
      ram: '45',
      disk: '67',
      uptime: '5d 14h'
    }
  };
  
  const dashboard = interface.formatAdminDashboard(data);
  
  await bot.editMessageText(dashboard, {
    chat_id: chatId,
    message_id: messageId,
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔄 Atualizar', callback_data: 'menu_dashboard' },
          { text: '📊 Relatório', callback_data: 'admin_report' }
        ],
        [
          { text: '👥 Usuários', callback_data: 'admin_users' },
          { text: '⚙️ Config', callback_data: 'admin_config' }
        ],
        [
          { text: '↩️ Menu Principal', callback_data: 'menu_main' }
        ]
      ]
    }
  });
}

// Função para fazer parse do comando /scan
function parseCommand(input) {
  const parts = input.split(' ');
  const url = parts[0];

  // Verificar se tem parâmetro -l (login)
  const loginIndex = parts.indexOf('-l');
  let credentials = null;

  if (loginIndex !== -1 && parts[loginIndex + 1]) {
    const credentialString = parts[loginIndex + 1];
    const [username, password] = credentialString.split(':');

    if (username && password) {
      credentials = { username, password };
    }
  }

  return {
    url: url.match(/^https?:\/\/.+/) ? url : null,
    credentials
  };
}

// Função para gerar login usando openai-agents-mcp
async function generateLoginWithMCP(url, username, password) {
  return new Promise((resolve, reject) => {
    console.log('🐍 Executando script Python com openai-agents-mcp...');

    const pythonProcess = spawn('python', [
      'login_generator_simple.py',
      url,
      username,
      password
    ], {
      cwd: process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          // Extrair JSON do output
          const lines = stdout.split('\n');
          const jsonStartIndex = lines.findIndex(line => line.includes('📋 JSON Result:'));

          if (jsonStartIndex !== -1) {
            const jsonLines = lines.slice(jsonStartIndex + 1);
            const jsonString = jsonLines.join('\n').trim();
            const result = JSON.parse(jsonString);

            console.log('✅ Script Python executado com sucesso');
            resolve(result);
          } else {
            throw new Error('JSON result não encontrado no output');
          }
        } catch (error) {
          console.error('❌ Erro ao processar output do Python:', error);
          console.log('📋 Output completo:', stdout);
          reject(new Error(`Erro ao processar resultado: ${error.message}`));
        }
      } else {
        console.error('❌ Script Python falhou:', stderr);
        reject(new Error(`Script Python falhou (código ${code}): ${stderr}`));
      }
    });

    pythonProcess.on('error', (error) => {
      console.error('❌ Erro ao executar Python:', error);
      reject(new Error(`Erro ao executar Python: ${error.message}`));
    });
  });
}

// Função para lidar com scan com login
async function handleScanWithLogin(chatId, messageId, parsedCommand) {
  try {
    // Atualizar status
    await bot.editMessageText(
      `🔍 **Scan com Login**\n\n` +
      `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
      `🔐 Login: ✅ ${escapeMarkdown(parsedCommand.credentials.username)}\n` +
      `⏳ Status: Gerando sequência de login com IA...`,
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      }
    );

    // Gerar sequência de login com openai-agents-mcp
    const loginResult = await generateLoginWithMCP(
      parsedCommand.url,
      parsedCommand.credentials.username,
      parsedCommand.credentials.password
    );

    // Atualizar status
    await bot.editMessageText(
      `🔍 **Scan com Login**\n\n` +
      `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
      `🔐 Login: ✅ Sequência gerada\n` +
      `📄 LSR: ${escapeMarkdown(loginResult.filename)}\n` +
      `⏳ Status: Adicionando à fila de scan...`,
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      }
    );

    // Salvar arquivo LSR
    const fs = require('fs');
    const path = require('path');

    const lsrPath = path.join('./lsr_files', loginResult.filename);
    fs.writeFileSync(lsrPath, JSON.stringify(loginResult.lsr, null, 2));

    // Adicionar à fila de scan
    const result = await scanQueue.addScan(
      chatId,
      parsedCommand.url,
      `Scan com Login - ${new Date().toISOString()}`,
      { lsrPath } // Passar como additionalData
    );

    if (result.success) {
      await bot.editMessageText(
        `✅ **Scan Adicionado com Sucesso!**\n\n` +
        `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
        `🔐 Login: ✅ Configurado\n` +
        `📄 LSR: ${escapeMarkdown(loginResult.filename)}\n` +
        `📍 Posição na fila: ${result.position}\n` +
        `⏱️ Tempo estimado: ${result.position * 5} minutos`,
        {
          chat_id: chatId,
          message_id: messageId,
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: [
              [
                { text: '📋 Ver Fila', callback_data: 'menu_queue' },
                { text: '➕ Novo Scan', callback_data: 'menu_scan' }
              ],
              [
                { text: '↩️ Menu Principal', callback_data: 'menu_main' }
              ]
            ]
          }
        }
      );
    } else {
      throw new Error(result.message);
    }

  } catch (error) {
    console.error('Erro no scan com login:', error);
    await bot.editMessageText(
      `❌ **Erro no Scan com Login**\n\n` +
      `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
      `❌ Erro: ${escapeMarkdown(error.message)}`,
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      }
    );
  }
}

// Função para lidar com scan normal
async function handleNormalScan(chatId, messageId, parsedCommand) {
  try {
    // Atualizar status
    await bot.editMessageText(
      `🔍 **Scan Normal**\n\n` +
      `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
      `⏳ Status: Adicionando à fila...`,
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      }
    );

    // Adicionar à fila de scan
    const result = await scanQueue.addScan(
      chatId,
      parsedCommand.url,
      `Scan Normal - ${new Date().toISOString()}`
    );

    if (result.success) {
      await bot.editMessageText(
        `✅ **Scan Adicionado com Sucesso!**\n\n` +
        `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
        `📍 Posição na fila: ${result.position}\n` +
        `⏱️ Tempo estimado: ${result.position * 5} minutos`,
        {
          chat_id: chatId,
          message_id: messageId,
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: [
              [
                { text: '📋 Ver Fila', callback_data: 'menu_queue' },
                { text: '➕ Novo Scan', callback_data: 'menu_scan' }
              ],
              [
                { text: '↩️ Menu Principal', callback_data: 'menu_main' }
              ]
            ]
          }
        }
      );
    } else {
      throw new Error(result.message);
    }

  } catch (error) {
    console.error('Erro no scan normal:', error);
    await bot.editMessageText(
      `❌ **Erro no Scan**\n\n` +
      `🎯 Target: ${escapeMarkdown(parsedCommand.url)}\n` +
      `❌ Erro: ${escapeMarkdown(error.message)}`,
      {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      }
    );
  }
}

// Limpar sessões expiradas
setInterval(() => {
  sessionManager.cleanupExpiredSessions();
}, 3600000);

console.log('🎨 Bot Acunetix Professional iniciado!');
console.log('🎯 Interface interativa ativada');
console.log('📊 Dashboard profissional disponível');
console.log('🎨 Sistema de templates customizáveis ativo');
console.log('🔧 Comando /scan com MCP integrado!');