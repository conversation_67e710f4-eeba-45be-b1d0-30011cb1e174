const EventEmitter = require('events');

class ScanQueueManager extends EventEmitter {
  constructor() {
    super();
    this.userQueues = new Map(); // Fila por usuário
    this.activeScans = new Map(); // Scans em execução
    this.scanHistory = new Map(); // Histórico de scans
    this.maxConcurrentScansPerUser = 2;
    this.maxTotalConcurrentScans = 5;
    this.duplicateCheckWindow = 300000; // 5 minutos em ms
  }

  // Adicionar scan à fila
  async addScan(userId, target, description, additionalData = {}) {
    const scanId = this.generateScanId();
    const timestamp = Date.now();
    
    // Verificar se é scan duplicado
    if (this.isDuplicateScan(userId, target)) {
      return {
        success: false,
        reason: 'duplicate',
        message: `Já existe um scan recente para ${target}. Aguarde alguns minutos antes de tentar novamente.`
      };
    }
    
    // Criar scan object
    const scan = {
      id: scanId,
      userId,
      target,
      description,
      status: 'queued',
      timestamp,
      attempts: 0,
      ...additionalData // Incluir dados adicionais como lsrPath
    };
    
    // Adicionar à fila do usuário
    if (!this.userQueues.has(userId)) {
      this.userQueues.set(userId, []);
    }
    
    this.userQueues.get(userId).push(scan);
    
    // Adicionar ao histórico
    this.addToHistory(userId, scan);
    
    // Processar fila
    this.processQueue();
    
    return {
      success: true,
      scanId,
      position: this.getQueuePosition(userId, scanId),
      message: `Scan adicionado à fila. Posição: ${this.getQueuePosition(userId, scanId)}`
    };
  }
  
  // Verificar se é scan duplicado
  isDuplicateScan(userId, target) {
    const userHistory = this.scanHistory.get(userId) || [];
    const now = Date.now();
    
    return userHistory.some(scan => 
      scan.target === target && 
      (now - scan.timestamp) < this.duplicateCheckWindow &&
      ['queued', 'running', 'completed'].includes(scan.status)
    );
  }
  
  // Gerar ID único para scan
  generateScanId() {
    return `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // Adicionar ao histórico
  addToHistory(userId, scan) {
    if (!this.scanHistory.has(userId)) {
      this.scanHistory.set(userId, []);
    }
    
    const history = this.scanHistory.get(userId);
    history.push({...scan});
    
    // Manter apenas últimos 100 scans por usuário
    if (history.length > 100) {
      history.shift();
    }
  }
  
  // Obter posição na fila
  getQueuePosition(userId, scanId) {
    const queue = this.userQueues.get(userId) || [];
    const position = queue.findIndex(scan => scan.id === scanId);
    return position + 1;
  }
  
  // Processar filas
  async processQueue() {
    const totalActive = this.activeScans.size;
    
    if (totalActive >= this.maxTotalConcurrentScans) {
      return; // Limite global atingido
    }
    
    // Processar cada fila de usuário
    for (const [userId, queue] of this.userQueues.entries()) {
      if (queue.length === 0) continue;
      
      const userActiveScans = this.getUserActiveScans(userId);
      
      if (userActiveScans >= this.maxConcurrentScansPerUser) {
        continue; // Limite por usuário atingido
      }
      
      // Pegar próximo scan da fila
      const nextScan = queue.shift();
      if (nextScan) {
        this.executeScan(nextScan);
      }
    }
  }
  
  // Contar scans ativos do usuário
  getUserActiveScans(userId) {
    let count = 0;
    for (const [, scan] of this.activeScans) {
      if (scan.userId === userId) count++;
    }
    return count;
  }
  
  // Executar scan
  async executeScan(scan) {
    scan.status = 'running';
    scan.startTime = Date.now();
    this.activeScans.set(scan.id, scan);
    
    this.emit('scan:started', scan);
    
    try {
      // Importar função de scan
      const { addTargetAndStartSQLIScan } = require('./add-target');
      
      // Executar scan (com ou sem LSR)
      const result = await addTargetAndStartSQLIScan(scan.target, scan.description, scan.lsrPath);
      
      scan.status = 'completed';
      scan.result = result;
      scan.endTime = Date.now();
      
      this.emit('scan:completed', scan);
      
    } catch (error) {
      scan.status = 'failed';
      scan.error = error.message;
      scan.attempts++;
      
      // Retry logic
      if (scan.attempts < 3) {
        scan.status = 'queued';
        scan.retryAfter = Date.now() + (60000 * scan.attempts); // Wait longer each retry
        
        // Re-adicionar à fila
        const queue = this.userQueues.get(scan.userId) || [];
        queue.push(scan);
        this.userQueues.set(scan.userId, queue);
        
        this.emit('scan:retry', scan);
      } else {
        scan.endTime = Date.now();
        this.emit('scan:failed', scan);
      }
    } finally {
      this.activeScans.delete(scan.id);
      this.updateHistory(scan);
      
      // Processar próximo da fila
      setTimeout(() => this.processQueue(), 1000);
    }
  }
  
  // Atualizar histórico
  updateHistory(scan) {
    const history = this.scanHistory.get(scan.userId) || [];
    const index = history.findIndex(s => s.id === scan.id);
    
    if (index !== -1) {
      history[index] = {...scan};
    }
  }
  
  // Obter status da fila do usuário
  getUserQueueStatus(userId) {
    const queue = this.userQueues.get(userId) || [];
    const activeScans = [];
    
    for (const [, scan] of this.activeScans) {
      if (scan.userId === userId) {
        activeScans.push(scan);
      }
    }
    
    return {
      queued: queue.length,
      active: activeScans.length,
      queue: queue.map(s => ({
        id: s.id,
        target: s.target,
        position: this.getQueuePosition(userId, s.id)
      })),
      activeScans: activeScans.map(s => ({
        id: s.id,
        target: s.target,
        startTime: s.startTime
      }))
    };
  }
  
  // Cancelar scan
  cancelScan(userId, scanId) {
    // Verificar se está na fila
    const queue = this.userQueues.get(userId) || [];
    const queueIndex = queue.findIndex(s => s.id === scanId);
    
    if (queueIndex !== -1) {
      const scan = queue.splice(queueIndex, 1)[0];
      scan.status = 'cancelled';
      this.updateHistory(scan);
      
      return {
        success: true,
        message: 'Scan removido da fila'
      };
    }
    
    // Verificar se está ativo
    if (this.activeScans.has(scanId)) {
      return {
        success: false,
        message: 'Não é possível cancelar scan em execução'
      };
    }
    
    return {
      success: false,
      message: 'Scan não encontrado'
    };
  }
  
  // Obter histórico do usuário
  getUserHistory(userId, limit = 10) {
    const history = this.scanHistory.get(userId) || [];
    return history.slice(-limit).reverse();
  }
  
  // Limpar filas (para manutenção)
  clearUserQueue(userId) {
    this.userQueues.set(userId, []);
    return {
      success: true,
      message: 'Fila limpa com sucesso'
    };
  }
  
  // Estatísticas globais
  getGlobalStats() {
    let totalQueued = 0;
    let totalActive = this.activeScans.size;
    let userCount = 0;
    
    for (const [, queue] of this.userQueues) {
      if (queue.length > 0) userCount++;
      totalQueued += queue.length;
    }
    
    return {
      totalQueued,
      totalActive,
      activeUsers: userCount,
      maxConcurrent: this.maxTotalConcurrentScans
    };
  }
}

module.exports = ScanQueueManager;