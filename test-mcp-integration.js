/**
 * Script de teste para integração MCP
 * Testa o comando /scan com login usando a nova arquitetura
 */

const MCPClient = require('./mcp-client');

async function testMCPIntegration() {
  console.log('🧪 Testando integração MCP...\n');

  const mcpClient = new MCPClient();

  try {
    // Teste 1: Verificar se o cliente MCP inicializa
    console.log('1️⃣ Testando inicialização do cliente MCP...');
    console.log('✅ Cliente MCP criado com sucesso\n');

    // Teste 2: Testar geração de sequência de login
    console.log('2️⃣ Testando geração de sequência de login...');
    
    const testUrl = 'https://example.com/login';
    const testUser = '<EMAIL>';
    const testPass = 'password123';

    console.log(`📍 URL: ${testUrl}`);
    console.log(`👤 Usuário: ${testUser}`);
    console.log(`🔑 Senha: ${'*'.repeat(testPass.length)}\n`);

    const result = await mcpClient.generateLoginSequence(testUrl, testUser, testPass);

    console.log('✅ Sequência de login gerada com sucesso!');
    console.log(`📄 Arquivo LSR: ${result.filename}`);
    console.log(`📁 Caminho: ${result.path}`);
    console.log(`🆔 Ações executadas: ${result.actions.length}\n`);

    // Teste 3: Verificar estrutura do LSR
    console.log('3️⃣ Verificando estrutura do LSR...');
    console.log('📋 Ações registradas:');
    result.actions.forEach((action, index) => {
      console.log(`  ${index + 1}. ${action.type} - ${action.parameters.selector || action.parameters.url || 'N/A'}`);
    });

    console.log('\n✅ Todos os testes passaram!');
    console.log('🚀 Integração MCP está funcionando corretamente');

  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    console.error(error.stack);
  } finally {
    // Limpar recursos
    await mcpClient.disconnectAll();
    console.log('\n🧹 Recursos limpos');
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  testMCPIntegration();
}

module.exports = { testMCPIntegration };
